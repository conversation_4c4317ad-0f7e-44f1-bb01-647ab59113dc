#if defined(_WIN32)
#define NOMINMAX
#endif
#include "piano_keyboard.h"
#include "audio_engine.h"
#include "config_manager.h"
#include <algorithm>
#include <cmath>
#include <iostream>

PianoKeyboard::PianoKeyboard()
    : keyboard_position_(50.0f, 80.0f)
    , keyboard_size_(1200.0f, 200.0f)
    , white_key_size_(20.0f, 260.0f)
    , black_key_size_(16.0f, 160.0f)
    , auto_layout_enabled_(true)
    , keyboard_margin_(50.0f)
    , current_window_width_(1280)
    , current_window_height_(720)
    , white_key_color_(Color::FromRGB(255, 255, 255))
    , white_key_pressed_color_(Color::FromRGB(150, 150, 150))
    , black_key_color_(Color::FromRGB(30, 30, 30))
    , black_key_pressed_color_(Color::FromRGB(150, 150, 150))
    , white_key_midi_pressed_color_(Color::FromRGB(200, 0, 0))
    , black_key_midi_pressed_color_(Color::FromRGB(200, 0, 0))
    , key_border_color_(Color::FromRGB(50, 50, 50))
    , audio_engine_(nullptr)
    , audio_enabled_(true)
    , last_hovered_key_(-1)
    , config_(nullptr)
    , multioctave_enabled_(false)
    , multioctave_count_(1)
    , multioctave_distance_(12)
    , current_mouse_y_(-1.0)
    , last_calculated_velocity_(127)
    , white_blip_width_(0.0f)  // Will be set to match key width
    , white_blip_height_(10.0f)
    , white_blip_x_offset_(0.0f)  // Will be set to match key position
    , white_blip_y_offset_(0.0f)  // Will be set to match key position
    , black_blip_width_(0.0f)  // Will be set to match key width
    , black_blip_height_(8.0f)
    , black_blip_x_offset_(0.0f)  // Will be set to match key position
    , black_blip_y_offset_(0.0f)  // Will be set to match key position
    , blip_fade_duration_ms_(1000.0f)
    , blip_spacing_factor_(1.2f)
    , key_press_animation_duration_ms_(80.0f)   // 80ms animation duration
    , key_release_animation_duration_ms_(90.0f) // 80ms animation duration (same as press)
    , key_press_scale_factor_(0.95f)             // Scale to 95% when pressed
    , key_press_y_offset_(2.0f)                  // Move 2px down when pressed
    , rainbow_color_index_(0)
    , last_rainbow_update_(std::chrono::steady_clock::now())
    , last_key_rainbow_update_(std::chrono::steady_clock::now())
{
    InitializeRainbowColors();
}

PianoKeyboard::~PianoKeyboard() {
}

void PianoKeyboard::Initialize() {
    Initialize(nullptr);
}

void PianoKeyboard::Initialize(AudioEngine* audio_engine) {
    audio_engine_ = audio_engine;

    keys_.clear();
    keys_.reserve(128);

    for (int note = 0; note < 128; ++note) {
        PianoKey key;
        key.note = note;
        key.is_black = IsBlackKey(note);
        key.is_pressed = false;
        key.is_midi_pressed = false;
        key.midi_channel = -1; // -1 indicates no MIDI channel assigned
        key.color = key.is_black ? black_key_color_ : white_key_color_;

        // Initialize animation properties
        key.was_pressed = false;
        key.was_midi_pressed = false;
        key.press_time = std::chrono::steady_clock::now();
        key.release_time = std::chrono::steady_clock::now();
        key.animation_progress = 0.0f;
        key.is_animating = false;

        keys_.push_back(key);
    }

    CalculateKeyPositions();
}

void PianoKeyboard::Update() {
/*     // Update key colors based on pressed state
    for (auto& key : keys_) {
        if (key.is_midi_pressed) {
            // MIDI input takes priority for visual feedback - use default MIDI colors for key highlight
            key.color = key.is_black ? black_key_midi_pressed_color_ : white_key_midi_pressed_color_;
        } else if (key.is_pressed) {
            // Mouse input
            key.color = key.is_black ? black_key_pressed_color_ : white_key_pressed_color_;
        } else {
            // Default state
            key.color = key.is_black ? black_key_color_ : white_key_color_;
        }
    } */

    // Update rainbow colors
    UpdateRainbowColors();

    // Update blips
    UpdateBlips();

    // Update key animations
    UpdateKeyAnimations();
}

void PianoKeyboard::Render(OpenGLRenderer& renderer) {
    // Layer 1: Render white keys (background)
    RenderWhiteKeys(renderer);

    // Layer 2: Render white key blips
    RenderWhiteKeyBlips(renderer);

    // Layer 3: Render black keys
    RenderBlackKeys(renderer);

    // Layer 4: Render black key blips (top layer)
    RenderBlackKeyBlips(renderer);
}

void PianoKeyboard::HandleInput(double mouse_x, double mouse_y, bool mouse_is_down) {
    Vec2 mouse_pos(mouse_x, mouse_y);
    int current_key = GetKeyAtPosition(mouse_pos);

    // Store current mouse Y position for velocity calculation
    current_mouse_y_ = mouse_y;

    if (mouse_is_down) {
        if (current_key != last_hovered_key_) {
            // Stop the last key if we slid off it
            if (last_hovered_key_ != -1) {
                SetKeyPressedWithVelocity(last_hovered_key_, false, mouse_y);
            }
            // Start the new key
            if (current_key != -1) {
                SetKeyPressedWithVelocity(current_key, true, mouse_y);
            }
            last_hovered_key_ = current_key;
        }
    } else {
        // Mouse is up, release any pressed key
        if (last_hovered_key_ != -1) {
            SetKeyPressedWithVelocity(last_hovered_key_, false, mouse_y);
            last_hovered_key_ = -1;
        }
    }
}

bool PianoKeyboard::IsKeyPressed(int note) const {
    if (note >= 0 && note < 128) {
        return keys_[note].is_pressed;
    }
    return false;
}

void PianoKeyboard::SetKeyPressed(int note, bool pressed) {
    // Use default velocity (127) for backward compatibility
    SetKeyPressedWithVelocity(note, pressed, -1.0);
}

bool PianoKeyboard::IsMIDIKeyPressed(int note) const {
    if (note >= 0 && note < 128) {
        return keys_[note].is_midi_pressed;
    }
    return false;
}

void PianoKeyboard::SetMIDIKeyPressed(int note, bool pressed) {
    // Call the overloaded version with channel -1 (no specific channel)
    SetMIDIKeyPressed(note, pressed, -1);
}

void PianoKeyboard::SetMIDIKeyPressed(int note, bool pressed, int channel) {
    if (note >= 0 && note < 128) {
        bool was_pressed = keys_[note].is_midi_pressed;
        keys_[note].is_midi_pressed = pressed;

        if (pressed) {
            keys_[note].midi_channel = channel;
            // std::cout << "Piano Keyboard: Setting MIDI key " << note << " pressed on channel " << channel << std::endl;
        } else {
            keys_[note].midi_channel = -1;
            // std::cout << "Piano Keyboard: Setting MIDI key " << note << " released" << std::endl;
        }

        // Add visual blip effect when MIDI key is pressed
        if (pressed && !was_pressed) {
            Color blip_color;

            // Determine color based on configuration
            if (config_ && config_->midi.color_type > 0) {
                // Use rainbow colors (default velocity 64 for backward compatibility)
                blip_color = GetRainbowColor(config_->midi.color_type, note, 64);
            } else if (channel >= 0 && channel < 16) {
                // Use channel-specific color
                blip_color = GetChannelColor(channel);
                //std::cout << "Piano Keyboard: Using channel color for channel " << channel
                //         << " - R:" << blip_color.r << " G:" << blip_color.g << " B:" << blip_color.b << std::endl;
            } else {
                // Fallback to default MIDI colors
                blip_color = keys_[note].is_black ?
                     black_key_midi_pressed_color_ :  white_key_midi_pressed_color_;
                // std::cout << "Piano Keyboard: Using default MIDI color for invalid channel " << channel << std::endl;
            }
            AddKeyBlip(note, blip_color);
        }

        // Note: Audio playback for MIDI input is handled by AudioEngine directly
        // This method is only for visual feedback
    }
}

void PianoKeyboard::SetMIDIKeyPressed(int note, bool pressed, int channel, int velocity) {
    if (note >= 0 && note < 128) {
        bool was_pressed = keys_[note].is_midi_pressed;
        keys_[note].is_midi_pressed = pressed;

        if (pressed) {
            keys_[note].midi_channel = channel;
            // std::cout << "Piano Keyboard: Setting MIDI key " << note << " pressed on channel " << channel << " with velocity " << velocity << std::endl;
        } else {
            keys_[note].midi_channel = -1;
            // std::cout << "Piano Keyboard: Setting MIDI key " << note << " released" << std::endl;
        }

        // Add visual blip effect when MIDI key is pressed
        if (pressed && !was_pressed) {
            Color blip_color;

            // Determine color based on configuration
            if (config_ && config_->midi.color_type > 0) {
                // Use rainbow colors (pass velocity for velocity-rainbow mode)
                blip_color = GetRainbowColor(config_->midi.color_type, note, velocity);
            } else if (channel >= 0 && channel < 16) {
                // Use channel-specific color
                blip_color = GetChannelColor(channel);
                //std::cout << "Piano Keyboard: Using channel color for channel " << channel
                //         << " - R:" << blip_color.r << " G:" << blip_color.g << " B:" << blip_color.b << std::endl;
            } else {
                // Fallback to default MIDI colors
                blip_color = keys_[note].is_black ?
                     black_key_midi_pressed_color_ :  white_key_midi_pressed_color_;
                // std::cout << "Piano Keyboard: Using default MIDI color for invalid channel " << channel << std::endl;
            }
            AddKeyBlip(note, blip_color);
        }

        // Note: Audio playback for MIDI input is handled by AudioEngine directly
        // This method is only for visual feedback
    }
}

void PianoKeyboard::SetKeyboardPosition(const Vec2& position) {
    keyboard_position_ = position;
    CalculateKeyPositions();
}

void PianoKeyboard::SetKeyboardSize(const Vec2& size) {
    keyboard_size_ = size;
    CalculateKeyPositions();
}

void PianoKeyboard::SetWhiteKeySize(const Vec2& size) {
    white_key_size_ = size;
    CalculateKeyPositions();
}

void PianoKeyboard::SetBlackKeySize(const Vec2& size) {
    black_key_size_ = size;
    CalculateKeyPositions();
}

void PianoKeyboard::UpdateLayout(int window_width, int window_height) {
    current_window_width_ = window_width;
    current_window_height_ = window_height;

    if (auto_layout_enabled_) {
        CalculateAutoLayout(window_width, window_height);
    }
}

void PianoKeyboard::SetAutoLayout(bool enabled) {
    auto_layout_enabled_ = enabled;
    if (enabled) {
        CalculateAutoLayout(current_window_width_, current_window_height_);
    }
}

void PianoKeyboard::SetKeyboardMargin(float margin) {
    keyboard_margin_ = margin;
    if (auto_layout_enabled_) {
        CalculateAutoLayout(current_window_width_, current_window_height_);
    }
}

int PianoKeyboard::GetPressedKeyCount() const {
    return std::count_if(keys_.begin(), keys_.end(), 
                        [](const PianoKey& key) { return key.is_pressed; });
}

std::vector<int> PianoKeyboard::GetPressedKeys() const {
    std::vector<int> pressed_keys;
    for (const auto& key : keys_) {
        if (key.is_pressed) {
            pressed_keys.push_back(key.note);
        }
    }
    return pressed_keys;
}

int PianoKeyboard::GetTotalBlipCount() const {
    int total_blips = 0;
    for (const auto& key : keys_) {
        total_blips += static_cast<int>(key.blips.size());
    }
    return total_blips;
}

bool PianoKeyboard::IsBlackKey(int note) const {
    int octave_note = note % 12;
    return (octave_note == 1 || octave_note == 3 || octave_note == 6 || 
            octave_note == 8 || octave_note == 10);
}

void PianoKeyboard::CalculateKeyPositions() {
    // Calculate white key positions first
    float white_key_x = keyboard_position_.x;
    int white_key_count = 0;

    for (auto& key : keys_) {
        if (!key.is_black) {
            key.position = Vec2(white_key_x, keyboard_position_.y);
            key.size = white_key_size_;
            white_key_x += white_key_size_.x;
            white_key_count++;
        }
    }

    // Calculate black key positions
    for (auto& key : keys_) {
        if (key.is_black) {
            int white_key_index = GetWhiteKeyIndex(key.note);
            if (white_key_index >= 0) {
                float black_key_x = keyboard_position_.x + (white_key_index * white_key_size_.x) - (black_key_size_.x * 0.5f);
                key.position = Vec2(black_key_x, keyboard_position_.y);
                key.size = black_key_size_;
            }
        }
    }

    // Update blip dimensions to match key sizes
    white_blip_width_ = white_key_size_.x;
    white_blip_x_offset_ = 0.0f;  // Align with key left edge
    white_blip_y_offset_ = 0.0f;  // Align with key top edge

    black_blip_width_ = black_key_size_.x;
    black_blip_x_offset_ = 0.0f;  // Align with key left edge
    black_blip_y_offset_ = 0.0f;  // Align with key top edge
}

int PianoKeyboard::GetWhiteKeyIndex(int note) const {
    int white_key_index = 0;
    for (int i = 0; i < note; ++i) {
        if (!IsBlackKey(i)) {
            white_key_index++;
        }
    }
    return white_key_index;
}

void PianoKeyboard::RenderWhiteKeys(OpenGLRenderer& renderer) {
    for (const auto& key : keys_) {
        if (!key.is_black) {
            // Calculate animated position and size
            Vec2 animated_position = key.position;
            Vec2 animated_size = key.size;

            if (key.is_animating && key.animation_progress > 0.0f) {
                // Apply scale animation
                float scale = 1.0f - (1.0f - key_press_scale_factor_) * key.animation_progress;
                animated_size.x *= scale;
                animated_size.y *= scale;

                // Center the scaled key
                animated_position.x += (key.size.x - animated_size.x) * 0.5f;
                animated_position.y += (key.size.y - animated_size.y) * 0.5f;

                // Apply vertical offset
                animated_position.y += key_press_y_offset_ * key.animation_progress;
            }

            // Render the white key with gradient (888888 to FFFFFF) - force opaque alpha
            Color white_top_color = Color::FromRGB(136, 136, 136, 255);    // 888888 with full opacity
            Color white_bottom_color = Color::FromRGB(255, 255, 255, 255); // FFFFFF with full opacity
            renderer.DrawRectGradientRounded(animated_position, animated_size,
                                    white_top_color, white_bottom_color, 6.0f);

            // Draw border with full opacity
            Color opaque_border_color = Color(key_border_color_.r, key_border_color_.g, key_border_color_.b, 1.0f);
            renderer.DrawRectWithRoundedBorder(animated_position, animated_size,
                                      Color(0, 0, 0, 0), opaque_border_color, 4.0f, 6.0f);
        }
    }
}

void PianoKeyboard::RenderBlackKeys(OpenGLRenderer& renderer) {
    for (const auto& key : keys_) {
        if (key.is_black) {
            // Calculate animated position and size
            Vec2 animated_position = key.position;
            Vec2 animated_size = key.size;

            if (key.is_animating && key.animation_progress > 0.0f) {
                // Apply scale animation
                float scale = 1.0f - (1.0f - key_press_scale_factor_) * key.animation_progress;
                animated_size.x *= scale;
                animated_size.y *= scale;

                // Center the scaled key
                animated_position.x += (key.size.x - animated_size.x) * 0.5f;
                animated_position.y += (key.size.y - animated_size.y) * 0.5f;

                // Apply vertical offset
                animated_position.y += key_press_y_offset_ * key.animation_progress;
            }

            // Render the black key with gradient (000000 to 444444) - force opaque alpha
            Color black_top_color = Color::FromRGB(0, 0, 0, 255);          // 000000 with full opacity
            Color black_bottom_color = Color::FromRGB(68, 68, 68, 255);    // 444444 with full opacity
            renderer.DrawRectGradientRounded(animated_position, animated_size,
                                    black_top_color, black_bottom_color, 6.0f);

            // Draw border with full opacity
            Color opaque_border_color = Color(key_border_color_.r, key_border_color_.g, key_border_color_.b, 1.0f);
            renderer.DrawRectWithRoundedBorder(animated_position, animated_size,
                                      Color(0, 0, 0, 0), opaque_border_color, 1.0f, 6.0f);
        }
    }
}

int PianoKeyboard::GetKeyAtPosition(const Vec2& pos) const {
    // Check black keys first (they're on top)
    for (const auto& key : keys_) {
        if (key.is_black) {
            if (pos.x >= key.position.x && pos.x <= key.position.x + key.size.x &&
                pos.y >= key.position.y && pos.y <= key.position.y + key.size.y) {
                return key.note;
            }
        }
    }

    // Then check white keys
    for (const auto& key : keys_) {
        if (!key.is_black) {
            if (pos.x >= key.position.x && pos.x <= key.position.x + key.size.x &&
                pos.y >= key.position.y && pos.y <= key.position.y + key.size.y) {
                return key.note;
            }
        }
    }

    return -1; // No key found
}

void PianoKeyboard::CalculateAutoLayout(int window_width, int window_height) {
    // Calculate total number of white keys
    int total_white_keys = GetTotalWhiteKeys();

    // Calculate available width for keyboard (minus margins)
    float available_width = window_width - (keyboard_margin_ * 2.0f);

    // Calculate white key width based on available space
    float white_key_width = available_width / total_white_keys;

    // Limit minimum and maximum key sizes for usability
    white_key_width = std::max(10.0f, std::min(white_key_width, 50.0f));

    // Use fixed keyboard height (don't change with window height)
    float white_key_height = 260.0f;
    float black_key_height = 140.0f;

    // Update key sizes
    white_key_size_ = Vec2(white_key_width, white_key_height);
    black_key_size_ = Vec2(white_key_width * 0.75f, black_key_height);

    // Calculate total keyboard width
    float total_keyboard_width = total_white_keys * white_key_width;

    // Center the keyboard horizontally
    float keyboard_x = (window_width - total_keyboard_width) * 0.5f;

    // Position keyboard in the center of the window
    float keyboard_y = (window_height - white_key_height) * 0.5f;

    keyboard_position_ = Vec2(keyboard_x, keyboard_y);
    keyboard_size_ = Vec2(total_keyboard_width, white_key_height);

    // Recalculate key positions
    CalculateKeyPositions();
}

int PianoKeyboard::GetTotalWhiteKeys() const {
    int count = 0;
    for (int i = 0; i < 128; ++i) {
        if (!IsBlackKey(i)) {
            count++;
        }
    }
    return count;
}

void PianoKeyboard::SetAudioEngine(AudioEngine* audio_engine) {
    audio_engine_ = audio_engine;
}

void PianoKeyboard::SetAudioEnabled(bool enabled) {
    audio_enabled_ = enabled;

    // If disabling audio, stop all currently playing notes
    if (!enabled && audio_engine_) {
        audio_engine_->StopAllNotes();
    }
}

bool PianoKeyboard::IsAudioEnabled() const {
    return audio_enabled_;
}

void PianoKeyboard::AddKeyBlip(int note, const Color& color) {
    if (note >= 0 && note < 128) {
        // Calculate maximum blips based on keyboard height and blip spacing
        float key_height = keys_[note].is_black ? black_key_size_.y : white_key_size_.y;
        float blip_height = keys_[note].is_black ? black_blip_height_ : white_blip_height_;
        float spacing = blip_height * blip_spacing_factor_;

        // Calculate how many blips can fit in the key height
        size_t max_blips_for_key = static_cast<size_t>(std::max(1.0f, key_height / spacing));

        // Ensure we don't exceed a reasonable maximum to prevent memory issues
        const size_t ABSOLUTE_MAX_BLIPS = 50;
        max_blips_for_key = std::min(max_blips_for_key, ABSOLUTE_MAX_BLIPS);

        if (keys_[note].blips.size() >= max_blips_for_key) {
            // Remove oldest blips if we exceed the limit
            keys_[note].blips.erase(keys_[note].blips.begin(),
                                   keys_[note].blips.begin() + (keys_[note].blips.size() - max_blips_for_key + 1));
        }

        KeyBlip blip;
        blip.time = std::chrono::steady_clock::now();
        blip.color = color;
        blip.y_offset = 0.0f; // Not used in the new implementation, but kept for compatibility

        keys_[note].blips.push_back(blip);
        keys_[note].time_played = blip.time;
    }
}

void PianoKeyboard::UpdateBlips() {
    auto now = std::chrono::steady_clock::now();

    for (auto& key : keys_) {
        if (key.blips.empty()) continue;

        // Use remove_if for more efficient removal of expired blips
        key.blips.erase(
            std::remove_if(key.blips.begin(), key.blips.end(),
                [now, this](const KeyBlip& blip) {
                    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - blip.time);
                    return elapsed.count() > blip_fade_duration_ms_;
                }),
            key.blips.end()
        );

        // Additional safety: limit total blips per key based on key height
        float key_height = key.is_black ? black_key_size_.y : white_key_size_.y;
        float blip_height = key.is_black ? black_blip_height_ : white_blip_height_;
        float spacing = blip_height * blip_spacing_factor_;

        size_t max_blips_for_key = static_cast<size_t>(std::max(1.0f, key_height / spacing));
        const size_t ABSOLUTE_MAX_BLIPS = 50;
        max_blips_for_key = std::min(max_blips_for_key, ABSOLUTE_MAX_BLIPS);

        if (key.blips.size() > max_blips_for_key) {
            key.blips.erase(key.blips.begin(),
                           key.blips.begin() + (key.blips.size() - max_blips_for_key));
        }
    }
}

void PianoKeyboard::RenderWhiteKeyBlips(OpenGLRenderer& renderer) {
    auto now = std::chrono::steady_clock::now();

    for (const auto& key : keys_) {
        // Only render blips for white keys
        if (key.is_black || key.blips.empty()) continue;

        // Add 4px margin around blips
        const float margin = 4.0f;

        // Use the key's actual position and size for blips with margin
        float blip_x = key.position.x + margin;
        float blip_width = key.size.x - (margin * 2.0f);  // Reduce width by margin on both sides
        float blip_height = white_blip_height_;

        // Start blips from the bottom of the key (on top of the key surface) with margin
        float blip_y = key.position.y + key.size.y - blip_height - margin;

        // Calculate the top boundary of the piano keyboard (top of the keys)
        float piano_top = keyboard_position_.y;

        // Render each blip for this key
        // Iterate from oldest to newest (index 0 is oldest, last index is newest)
        float current_y = blip_y;
        for (size_t i = 0; i < key.blips.size(); ++i) {
            const auto& blip = key.blips[i];
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - blip.time);
            float time_ratio = elapsed.count() / blip_fade_duration_ms_;

            // Calculate alpha based on time and position in stack
            // Older blips (lower index) fade out faster
            float base_alpha = 1.0f - time_ratio;

            // Additional fade based on position: older blips (lower in stack) get additional fade
            float position_fade = 1.0f - (static_cast<float>(i) / static_cast<float>(key.blips.size())) * 0.3f;
            float alpha = base_alpha * position_fade;

            if (alpha <= 0.0f) continue;

            // Clamp blip position to not go above the top of the piano
            if (current_y < piano_top) {
                // If the blip would be above the piano top, skip rendering it
                current_y -= blip_height * blip_spacing_factor_;
                continue;
            }

            // Create color with alpha - ensure blips are always opaque regardless of background
            Color blip_color = blip.color;
            // Force minimum alpha to ensure visibility even with transparent backgrounds
            float final_alpha = std::max(0.3f, std::min(1.0f, alpha));
            blip_color.a = final_alpha;

            // Calculate position - newer blips appear higher up
            Vec2 blip_pos(blip_x, current_y);
            Vec2 blip_size(blip_width, blip_height);

            // If the blip would extend above the piano top, clip its height
            if (current_y < piano_top) {
                float visible_height = current_y + blip_height - piano_top;
                if (visible_height > 0) {
                    blip_pos.y = piano_top;
                    blip_size.y = visible_height;
                } else {
                    // Blip is completely above the piano, skip it
                    current_y -= blip_height * blip_spacing_factor_;
                    continue;
                }
            }

            // Render the blip
            renderer.DrawRect(blip_pos, blip_size, blip_color);

            // Move to next blip position (stack them vertically upward)
            current_y -= blip_height * blip_spacing_factor_;
        }
    }
}

void PianoKeyboard::UpdateKeyAnimations() {
    auto now = std::chrono::steady_clock::now();

    for (auto& key : keys_) {
        bool currently_pressed = key.is_pressed || key.is_midi_pressed;
        bool was_pressed_before = key.was_pressed || key.was_midi_pressed;

        // Detect press events - only trigger animation on press
        if (currently_pressed && !was_pressed_before) {
            // Key was just pressed - start press animation
            key.press_time = now;
            key.is_animating = true;
            key.animation_progress = 0.0f;
        }

        // Update animation progress (only during the brief press animation)
        if (key.is_animating) {
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - key.press_time);
            float progress = elapsed.count() / key_press_animation_duration_ms_;

            if (progress >= 1.0f) {
                // Animation complete - stop animating and return to normal
                key.is_animating = false;
                key.animation_progress = 0.0f;
            } else {
                // Animation in progress
                key.animation_progress = progress;
            }
        }

        // Update previous state for next frame
        key.was_pressed = key.is_pressed;
        key.was_midi_pressed = key.is_midi_pressed;
    }
}

void PianoKeyboard::RenderBlackKeyBlips(OpenGLRenderer& renderer) {
    auto now = std::chrono::steady_clock::now();

    for (const auto& key : keys_) {
        // Only render blips for black keys
        if (!key.is_black || key.blips.empty()) continue;

        // Add 3px margin around blips
        const float margin = 3.0f;

        // Use the key's actual position and size for blips with margin
        float blip_x = key.position.x + margin;
        float blip_width = key.size.x - (margin * 2.0f);  // Reduce width by margin on both sides
        float blip_height = black_blip_height_;

        // Start blips from the bottom of the key (on top of the key surface) with margin
        float blip_y = key.position.y + key.size.y - blip_height - margin;

        // Calculate the top boundary of the piano keyboard (top of the keys)
        float piano_top = keyboard_position_.y;

        // Render each blip for this key
        // Iterate from oldest to newest (index 0 is oldest, last index is newest)
        float current_y = blip_y;
        for (size_t i = 0; i < key.blips.size(); ++i) {
            const auto& blip = key.blips[i];
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - blip.time);
            float time_ratio = elapsed.count() / blip_fade_duration_ms_;

            // Calculate alpha based on time and position in stack
            // Older blips (lower index) fade out faster
            float base_alpha = 1.0f - time_ratio;

            // Additional fade based on position: older blips (lower in stack) get additional fade
            float position_fade = 1.0f - (static_cast<float>(i) / static_cast<float>(key.blips.size())) * 0.3f;
            float alpha = base_alpha * position_fade;

            if (alpha <= 0.0f) continue;

            // Clamp blip position to not go above the top of the piano
            if (current_y < piano_top) {
                // If the blip would be above the piano top, skip rendering it
                current_y -= blip_height * blip_spacing_factor_;
                continue;
            }

            // Create color with alpha - ensure blips are always opaque regardless of background
            Color blip_color = blip.color;
            // Force minimum alpha to ensure visibility even with transparent backgrounds
            float final_alpha = std::max(0.3f, std::min(1.0f, alpha));
            blip_color.a = final_alpha;

            // Calculate position - newer blips appear higher up
            Vec2 blip_pos(blip_x, current_y);
            Vec2 blip_size(blip_width, blip_height);

            // If the blip would extend above the piano top, clip its height
            if (current_y < piano_top) {
                float visible_height = current_y + blip_height - piano_top;
                if (visible_height > 0) {
                    blip_pos.y = piano_top;
                    blip_size.y = visible_height;
                } else {
                    // Blip is completely above the piano, skip it
                    current_y -= blip_height * blip_spacing_factor_;
                    continue;
                }
            }

            // Render the blip
            renderer.DrawRect(blip_pos, blip_size, blip_color);

            // Move to next blip position (stack them vertically upward)
            current_y -= blip_height * blip_spacing_factor_;
        }
    }
}

Color PianoKeyboard::GetChannelColor(int channel) const {
    // Generate distinct colors for each MIDI channel (0-15)
    // Using HSV color space to ensure good color separation
    if (channel < 0 || channel >= 16) {
        // Fallback to default red color for invalid channels
        return Color::FromRGB(200, 0, 0);
    }

    // Define 16 distinct colors for MIDI channels
    static const Color channel_colors[16] = {
        Color::FromRGB(0x33, 0x66, 0xFF),  // Channel 0: #3366FF
        Color::FromRGB(0xFF, 0x7E, 0x33),  // Channel 1: #FF7E33
        Color::FromRGB(0x33, 0xFF, 0x66),  // Channel 2: #33FF66
        Color::FromRGB(0xFF, 0x33, 0x81),  // Channel 3: #FF3381
        Color::FromRGB(0x33, 0xFF, 0xFF),  // Channel 4: #33FFFF
        Color::FromRGB(0xE4, 0x33, 0xFF),  // Channel 5: #E433FF
        Color::FromRGB(0x99, 0xFF, 0x33),  // Channel 6: #99FF33
        Color::FromRGB(0x4B, 0x33, 0xFF),  // Channel 7: #4B33FF
        Color::FromRGB(0xFF, 0xCC, 0x33),  // Channel 8: #FFCC33
        Color::FromRGB(0x33, 0xB4, 0xFF),  // Channel 9: #33B4FF (Drums - typically channel 9)
        Color::FromRGB(0xFF, 0x33, 0x33),  // Channel 10: #FF3333
        Color::FromRGB(0x33, 0xFF, 0xB1),  // Channel 11: #33FFB1
        Color::FromRGB(0xFF, 0x33, 0xCC),  // Channel 12: #FF33CC
        Color::FromRGB(0x4E, 0xFF, 0x33),  // Channel 13: #4EFF33
        Color::FromRGB(0x99, 0x33, 0xFF),  // Channel 14: #9933FF
        Color::FromRGB(0xE7, 0xFF, 0x33)   // Channel 15: #E7FF33
    };

    return channel_colors[channel];
}

// Multioctave settings
void PianoKeyboard::SetMultioctaveEnabled(bool enabled) {
    multioctave_enabled_ = enabled;
}

bool PianoKeyboard::IsMultioctaveEnabled() const {
    return multioctave_enabled_;
}

void PianoKeyboard::SetMultioctaveCount(int count) {
    multioctave_count_ = std::max(0, std::min(count, 64)); // Limit to 0-64 octaves
}

int PianoKeyboard::GetMultioctaveCount() const {
    return multioctave_count_;
}

void PianoKeyboard::SetMultioctaveDistance(int distance) {
    multioctave_distance_ = std::max(1, std::min(distance, 24)); // Limit to 1-24 semitones
}

int PianoKeyboard::GetMultioctaveDistance() const {
    return multioctave_distance_;
}

void PianoKeyboard::SetKeyPressedWithVelocity(int note, bool pressed, double mouse_y) {
    if (note >= 0 && note < 128) {
        bool was_pressed = keys_[note].is_pressed;
        keys_[note].is_pressed = pressed;

        // Handle audio playback and visual effects (only for mouse/keyboard input, not MIDI input)
        if (pressed && !was_pressed) {
            // Calculate velocity based on mouse Y position
            int velocity = CalculateVelocityFromMouseY(mouse_y);

            // Add visual blip effect for mouse/keyboard input (blue color)
            Color blip_color = keys_[note].is_black ?
                black_key_pressed_color_ : white_key_pressed_color_;
            AddKeyBlip(note, blip_color);

            // Handle audio playback
            if (audio_engine_ && audio_enabled_) {
                // Key was just pressed - play note with calculated velocity
                audio_engine_->PlayNote(note, velocity);

                // Play multioctave notes if enabled
                if (multioctave_enabled_) {
                    for (int i = 1; i <= multioctave_count_; ++i) {
                        // Play note above
                        int note_above = note + (i * multioctave_distance_);
                        if (note_above < 128) {
                            audio_engine_->PlayNote(note_above, velocity);
                            AddKeyBlip(note_above, blip_color);
                        }

                        // Play note below
                        int note_below = note - (i * multioctave_distance_);
                        if (note_below >= 0) {
                            audio_engine_->PlayNote(note_below, velocity);
                            AddKeyBlip(note_below, blip_color);
                        }
                    }
                }
            }
        } else if (!pressed && was_pressed) {
            // Handle audio stop
            if (audio_engine_ && audio_enabled_) {
                // Key was just released - stop note
                audio_engine_->StopNote(note);

                // Stop multioctave notes if enabled
                if (multioctave_enabled_) {
                    for (int i = 1; i <= multioctave_count_; ++i) {
                        // Stop note above
                        int note_above = note + (i * multioctave_distance_);
                        if (note_above < 128) {
                            audio_engine_->StopNote(note_above);
                        }

                        // Stop note below
                        int note_below = note - (i * multioctave_distance_);
                        if (note_below >= 0) {
                            audio_engine_->StopNote(note_below);
                        }
                    }
                }
            }
        }
    }
}

int PianoKeyboard::CalculateVelocityFromMouseY(double mouse_y) const {
    // If mouse_y is invalid, return last calculated velocity
    if (mouse_y < 0.0) {
        return last_calculated_velocity_;
    }

    // Get the piano keyboard bounds
    float piano_top = keyboard_position_.y;
    float piano_bottom = keyboard_position_.y + white_key_size_.y;

    // If mouse is outside piano area, return last calculated velocity
    if (mouse_y < piano_top || mouse_y > piano_bottom) {
        return last_calculated_velocity_;
    }

    // Calculate relative position within piano (0.0 = top, 1.0 = bottom)
    float relative_y = (mouse_y - piano_top) / (piano_bottom - piano_top);

    // Clamp to valid range
    relative_y = std::max(0.0f, std::min(1.0f, relative_y));

    // Convert to velocity (top = low velocity, bottom = high velocity)
    // Map from range [0.0, 1.0] to velocity range [20, 127]
    int velocity = static_cast<int>(20 + (relative_y * (127 - 20)));
    velocity = std::max(1, std::min(127, velocity));

    // Store the calculated velocity for future use
    // Note: We need to cast away const here to update the cached velocity
    const_cast<PianoKeyboard*>(this)->last_calculated_velocity_ = velocity;

    return velocity;
}

// Configuration
void PianoKeyboard::SetConfig(const AppConfig* config) {
    config_ = config;
}

// Rainbow color methods
void PianoKeyboard::InitializeRainbowColors() {
    // Initialize rainbow colors (2048 colors for smooth animation)
    rainbow_colors_ = Rainbow::create(2048);

    // Initialize key-specific rainbow colors (128 colors for each MIDI note)
    key_rainbow_colors_ = Rainbow::create(128);

    // Note: velocity-rainbow colors are calculated dynamically in GetVelocityColor()
}

void PianoKeyboard::UpdateRainbowColors() {
    auto now = std::chrono::steady_clock::now();

    // Update main rainbow color index every 1ms (like the JavaScript setInterval)
    auto rainbow_elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_rainbow_update_);
    if (rainbow_elapsed.count() >= 1) {
        rainbow_color_index_++;
        if (rainbow_color_index_ >= 2048) {
            rainbow_color_index_ = 0;
        }
        last_rainbow_update_ = now;
    }

    // Update key rainbow colors every 50ms (like the JavaScript setInterval)
    auto key_rainbow_elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_key_rainbow_update_);
    if (key_rainbow_elapsed.count() >= 50) {
        if (config_ && config_->midi.color_type == 2) { // key-rainbow
            // Shift colors forward (like JavaScript shift/push)
            RainbowColor first_color = key_rainbow_colors_[0];
            for (size_t i = 0; i < key_rainbow_colors_.size() - 1; ++i) {
                key_rainbow_colors_[i] = key_rainbow_colors_[i + 1];
            }
            key_rainbow_colors_[key_rainbow_colors_.size() - 1] = first_color;
        } else if (config_ && config_->midi.color_type == 3) { // key-rainbow-reverse
            // Shift colors backward (like JavaScript pop/unshift)
            RainbowColor last_color = key_rainbow_colors_[key_rainbow_colors_.size() - 1];
            for (int i = key_rainbow_colors_.size() - 1; i > 0; --i) {
                key_rainbow_colors_[i] = key_rainbow_colors_[i - 1];
            }
            key_rainbow_colors_[0] = last_color;
        }
        last_key_rainbow_update_ = now;
    }
}

Color PianoKeyboard::GetRainbowColor(int color_type, int note, int velocity) const {
    switch (color_type) {
        case 1: // rainbow
            if (rainbow_color_index_ < rainbow_colors_.size()) {
                const RainbowColor& rc = rainbow_colors_[rainbow_color_index_];
                return Color::FromRGB(rc.r, rc.g, rc.b);
            }
            break;
        case 2: // key-rainbow
        case 3: // key-rainbow-reverse
            if (note >= 0 && note < 128 && note < static_cast<int>(key_rainbow_colors_.size())) {
                const RainbowColor& rc = key_rainbow_colors_[note];
                return Color::FromRGB(rc.r, rc.g, rc.b);
            }
            break;
        case 4: // velocity-rainbow
            {
                // Clamp velocity to valid range (0-127)
                int clamped_velocity = std::max(0, std::min(127, velocity));
                return GetVelocityColor(clamped_velocity);
            }
            break;
        default:
            break;
    }

    // Fallback to default color
    return Color::FromRGB(200, 0, 0);
}

Color PianoKeyboard::GetVelocityColor(int velocity) const {
    // Clamp velocity to valid range (0-127)
    velocity = std::max(0, std::min(127, velocity));

    // Normalize velocity to 0.0-1.0 range
    float normalized_velocity = static_cast<float>(velocity) / 127.0f;

    // Simple linear interpolation from pure blue to pure red
    // velocity 0: blue (0, 0, 255)
    // velocity 127: red (255, 0, 0)

    unsigned char r, g, b;

    // Red component: increases from 0 to 255
    r = static_cast<unsigned char>(normalized_velocity * 255);

    // Green component: stays at 0 for pure blue-to-red transition
    g = 0;

    // Blue component: decreases from 255 to 0
    b = static_cast<unsigned char>(255 * (1.0f - normalized_velocity));

    return Color::FromRGB(r, g, b);
}
